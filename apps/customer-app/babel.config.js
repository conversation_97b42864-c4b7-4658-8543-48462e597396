module.exports = function (api) {
  api.cache(true);
  
  return {
    presets: [
      ['babel-preset-expo', { jsxImportSource: 'nativewind' }]
    ],
    plugins: [
      [
        'module-resolver',
        {
          root: ['../..'],
          alias: {
            '@hvppyplug/common': '../../packages/common/src',
            '@hvppyplug/ui-components-v2': '../../packages/ui-components-v2/src',
            '@hvppyplug/mobile-services': '../../packages/mobile-services/src',
          },
          extensions: ['.js', '.jsx', '.ts', '.tsx', '.json'],
        },
      ],
      'react-native-reanimated/plugin', // Note: this must be last
    ],
  };
};
