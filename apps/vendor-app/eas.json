{"cli": {"version": ">= 12.0.0"}, "build": {"base": {"node": "20.11.1", "pnpm": "8.15.0", "env": {"EXPO_USE_METRO_WORKSPACE_ROOT": "1", "PNPM_CACHE_FOLDER": ".pnpm-cache", "CI": "1"}, "cache": {"disabled": false, "cacheDefaultPaths": true, "customPaths": ["node_modules/.cache", "../../node_modules/.cache", ".pnpm-cache"]}}, "development": {"extends": "base", "developmentClient": true, "distribution": "internal", "ios": {"resourceClass": "m-medium", "simulator": true, "buildConfiguration": "Debug"}, "android": {"resourceClass": "medium", "buildType": "apk", "gradleCommand": ":app:assembleDebug"}, "channel": "development", "env": {"EXPO_DEBUG": "true", "NODE_ENV": "development", "EXPO_USE_METRO_WORKSPACE_ROOT": "1"}}, "preview": {"extends": "base", "distribution": "internal", "ios": {"resourceClass": "m-medium", "simulator": false, "buildConfiguration": "Release"}, "android": {"resourceClass": "medium", "buildType": "apk", "gradleCommand": ":app:assembleRelease"}, "channel": "preview", "env": {"NODE_ENV": "production", "EXPO_USE_METRO_WORKSPACE_ROOT": "1"}}, "production": {"extends": "base", "autoIncrement": true, "ios": {"resourceClass": "m-large", "simulator": false, "buildConfiguration": "Release"}, "android": {"resourceClass": "large", "buildType": "app-bundle", "gradleCommand": ":app:bundleRelease"}, "channel": "production", "env": {"NODE_ENV": "production", "EXPO_USE_METRO_WORKSPACE_ROOT": "1"}}}, "submit": {"production": {"ios": {"appleId": "<EMAIL>", "ascAppId": "**********", "appleTeamId": "ABCDEFGHIJ"}, "android": {"serviceAccountKeyPath": "../path/to/api-key.json", "track": "internal"}}}}