{"name": "@hvppyplug/vendor-app", "version": "1.0.0", "main": "index.ts", "private": true, "scripts": {"start": "expo start", "dev": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build": "expo export", "clean": "expo r -c", "type-check": "tsc --noEmit", "lint": "eslint . --ext .ts,.tsx", "test": "jest", "prepare:build": "cd ../.. && ./scripts/prepare-eas-build.sh", "postinstall": "cd ../.. && pnpm install --frozen-lockfile && pnpm --filter @hvppyplug/common build && pnpm --filter @hvppyplug/ui-components-v2 build && pnpm --filter @hvppyplug/mobile-services build"}, "dependencies": {"@hvppyplug/common": "workspace:*", "@hvppyplug/mobile-services": "workspace:*", "@hvppyplug/ui-components-v2": "workspace:*", "expo": "~53.0.20", "expo-status-bar": "~2.2.3", "expo-constants": "~17.1.2", "expo-linking": "~7.1.2", "expo-notifications": "~0.30.1", "expo-location": "~18.0.4", "expo-image-picker": "~16.0.3", "expo-camera": "~16.0.8", "react": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "~2.20.2", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "^4.14.0", "react-native-screens": "^4.13.1", "react-native-maps": "^1.18.0", "@react-navigation/native": "^7.0.9", "@react-navigation/stack": "^7.1.1", "@react-navigation/bottom-tabs": "^7.1.5", "@tanstack/react-query": "^5.28.4", "zustand": "^4.5.2", "@react-native-async-storage/async-storage": "^2.1.0", "appwrite": "^16.0.2", "react-hook-form": "^7.51.2", "@hookform/resolvers": "^3.3.4", "zod": "^3.22.4", "date-fns": "^3.6.0", "react-native-chart-kit": "^6.12.0", "react-native-svg": "^15.2.0", "lucide-react-native": "^0.378.0", "nativewind": "^4.0.1", "tailwindcss": "^3.4.3", "framer-motion": "^11.0.0", "moti": "^0.28.1", "@react-native-community/blur": "^4.4.0", "react-native-haptic-feedback": "^2.2.0", "react-native-skeleton-placeholder": "^5.2.4", "@react-native-community/netinfo": "^11.3.1", "react-native-flash-message": "^0.4.2"}, "devDependencies": {"@babel/core": "^7.24.0", "@types/react": "~19.0.0", "@types/react-native": "~0.73.0", "@typescript-eslint/eslint-plugin": "^7.7.0", "@typescript-eslint/parser": "^7.7.0", "eslint": "^8.57.0", "eslint-config-expo": "^7.0.0", "jest": "^29.7.0", "typescript": "~5.3.3"}}